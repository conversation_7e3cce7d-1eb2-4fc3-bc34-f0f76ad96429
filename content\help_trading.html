📊 <b>Trading & Statistics Help</b>

Learn how to monitor your trading performance using the interactive interface:

<b>📈 Accessing Trading Data:</b>
• Use <b>View Stats</b> from the main menu for overall statistics
• Select any wallet to view <b>Trading History</b> and <b>Wallet Stats</b>
• Access <b>Detailed Stats</b> for comprehensive analytics

<b>📊 Available Statistics:</b>
• <b>Overall Stats</b> - Total wallets, combined balance, account overview
• <b>Wallet History</b> - Individual wallet trading history with details
• <b>Wallet Stats</b> - Success rates, trade volumes, and performance metrics
• <b>Detailed Analytics</b> - Comprehensive trading data and patterns

<b>📈 Trading History Features:</b>
• View recent trades with buy/sell indicators
• See transaction amounts and dates
• Monitor success/failure status
• Track trading patterns over time
• Filter by wallet or time period

<b>📊 Statistics & Analytics:</b>
• <b>Success Rates</b> - Percentage of successful trades
• <b>Trade Volumes</b> - Total and successful trading volumes
• <b>Buy/Sell Ratios</b> - Distribution of trade types
• <b>Performance Metrics</b> - Profit/loss tracking and analysis
• <b>Network Analysis</b> - Performance across different blockchains

<b>💡 Trading Insights:</b>
• Monitor success rates to improve strategy
• Track volume trends for better timing
• Analyze patterns across different networks
• Use historical data for decision making
• Compare performance between wallets

<b>📱 Navigation Tips:</b>
• Access stats from wallet details or main menu
• Use pagination for large trading histories
• Back buttons provide easy navigation
• Detailed stats offer comprehensive views

<b>🔍 Available Data Points:</b>
• Total trades count per wallet
• Success/failure rates and ratios
• Trading volumes in native tokens
• Buy vs sell transaction counts
• Profit/loss calculations
• Network-specific performance
• Time-based trading patterns

<b>⚠️ Important Notes:</b>
• Statistics update in real-time
• Historical data is preserved permanently
• Empty wallets show appropriate messages
• All amounts displayed in native tokens

Use the buttons below to navigate back:
