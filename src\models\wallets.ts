import { and, eq } from "drizzle-orm"
import { db } from "../db"
import { tableWallets } from "../db/schema/wallets"
import { log } from "../log"
import { dataChainName, type TypeChainName } from "../data"
import { BlockchainWallet } from "../blockchain/wallet"

/**
 * Wallet model class for managing user wallets
 *
 * This class provides methods for creating, retrieving, and managing wallet records
 * in the PostgreSQL database. Each wallet belongs to a user and is associated with
 * a specific blockchain network.
 */

export class Wallet {
  /**
   * Create a new wallet for a user
   * @param ownerId User ID (owner of the wallet)
   * @param name Wallet name (must be unique per user)
   * @param chain Blockchain network for this wallet
   * @param chainId Chain-specific identifier
   * @param address Wallet address
   * @param privateKey Privatekey for the wallet (will be encrypted before storage)
   * @param createdBy How the wallet was created ("system" or "import")
   * @param encryptionPassword Password to encrypt the private key with
   * @returns The created wallet or null if creation failed
   */
  static async create(ownerId: number, name: string, chain: TypeChainName, chainId: number, address: string, privateKey: string, createdBy: "system" | "import" = "system", encryptionPassword: string) {
    try {
      // Check if wallet name already exists for this user
      const existingWallet = await this.getByName(ownerId, name)
      if (existingWallet) {
        return null // Wallet name already exists
      }

      // Encrypt the private key before storing
      const encryptedPrivateKey = BlockchainWallet.encryptPrivateKey(privateKey, encryptionPassword)

      // Create new wallet
      await db
        .insert(tableWallets)
        .values({
          owner: ownerId,
          name,
          chain,
          chainId,
          address,
          balance: BigInt(0),
          privateKey: encryptedPrivateKey,
          createdBy
        })
        .execute()

      // Get the created wallet
      return this.getByName(ownerId, name)
    } catch (error) {
      log.error(`Failed Wallet:create: ${error}`)
      return null
    }
  }

  /**
   * Get a wallet by ID with ownership validation (SECURE)
   * @param id Wallet ID
   * @param ownerId User ID (owner of the wallet) - ensures user can only access their own wallets
   * @returns The wallet or null if not found or not owned by the user
   */
  static async getByIdForOwner(id: number, ownerId: number) {
    try {
      const wallet = await db
        .select()
        .from(tableWallets)
        .where(and(eq(tableWallets.id, id), eq(tableWallets.owner, ownerId)))
        .limit(1)
      return wallet[0] || null
    } catch (error) {
      log.error(`Failed Wallet:getByIdForOwner: ${error}`)
      return null
    }
  }

  /**
   * Get a wallet by name for a specific user
   * @param ownerId User ID (owner of the wallet)
   * @param name Wallet name
   * @returns The wallet or null if not found
   */
  static async getByName(ownerId: number, name: string) {
    try {
      const wallet = await db
        .select()
        .from(tableWallets)
        .where(and(eq(tableWallets.owner, ownerId), eq(tableWallets.name, name)))
        .limit(1)
      return wallet[0] || null
    } catch (error) {
      log.error(`Failed Wallet:getByName: ${error}`)
      return null
    }
  }

  /**
   * Get all wallets for an owner
   * @param ownerId User ID (owner of the wallets)
   * @returns Array of wallets
   */
  static async getAllForOwner(ownerId: number) {
    try {
      return await db.select().from(tableWallets).where(eq(tableWallets.owner, ownerId))
    } catch (error) {
      log.error(`Failed Wallet:getAllForOwner: ${error}`)
      return []
    }
  }

  /**
   * Import a wallet with a privatekey
   * @param ownerId User ID (owner of the wallet)
   * @param name Wallet name
   * @param chain Blockchain network
   * @param chainId Chain-specific identifier
   * @param address Wallet address
   * @param privateKey Privatekey for the wallet (will be encrypted before storage)
   * @param encryptionPassword Password to encrypt the private key with
   * @returns The imported wallet or null if import failed
   */
  static async import(ownerId: number, name: string, chain: TypeChainName, chainId: number, address: string, privateKey: string, encryptionPassword: string) {
    try {
      // Check if wallet name already exists for this user
      const existingWallet = await this.getByName(ownerId, name)
      if (existingWallet) {
        return null // Wallet name already exists
      }

      // Encrypt the private key before storing
      const encryptedPrivateKey = BlockchainWallet.encryptPrivateKey(privateKey, encryptionPassword)

      // Create new wallet with import flag
      await db
        .insert(tableWallets)
        .values({
          owner: ownerId,
          name,
          chain,
          chainId,
          address,
          balance: BigInt(0),
          privateKey: encryptedPrivateKey,
          createdBy: "import"
        })
        .execute()

      // Get the created wallet
      return this.getByName(ownerId, name)
    } catch (error) {
      log.error(`Failed Wallet:import: ${error}`)
      return null
    }
  }

  /**
   * Remove a wallet
   * @param id Wallet ID
   * @param ownerId User ID (for security verification)
   * @returns True if removal was successful, false otherwise
   */
  static async remove(id: number, ownerId: number) {
    try {
      // Use secure method to verify ownership
      const wallet = await this.getByIdForOwner(id, ownerId)
      if (!wallet) {
        return false
      }

      // Delete the wallet
      await db.delete(tableWallets).where(and(eq(tableWallets.id, id), eq(tableWallets.owner, ownerId)))

      return true
    } catch (error) {
      log.error(`Failed Wallet:remove: ${error}`)
      return false
    }
  }

  /**
   * Update wallet balance (SECURE - with ownership validation)
   * @param id Wallet ID
   * @param ownerId User ID (owner of the wallet) - ensures user can only update their own wallets
   * @param balance New balance amount
   * @returns Updated wallet or null if operation failed or wallet not owned by user
   */
  static async updateBalance(id: number, ownerId: number, balance: bigint) {
    try {
      // First verify ownership
      const wallet = await this.getByIdForOwner(id, ownerId)
      if (!wallet) {
        return null
      }

      // Update the balance with ownership constraint
      await db
        .update(tableWallets)
        .set({ balance })
        .where(and(eq(tableWallets.id, id), eq(tableWallets.owner, ownerId)))
        .execute()

      return this.getByIdForOwner(id, ownerId)
    } catch (error) {
      log.error(`Failed Wallet:updateBalance: ${error}`)
      return null
    }
  }

  /**
   * Get wallets by chain type
   * @param ownerId User ID (owner of the wallets)
   * @param chain Blockchain network to filter by
   * @returns Array of wallets for the specified chain
   */
  static async getByChain(ownerId: number, chain: TypeChainName) {
    try {
      return await db
        .select()
        .from(tableWallets)
        .where(and(eq(tableWallets.owner, ownerId), eq(tableWallets.chain, chain)))
    } catch (error) {
      log.error(`Failed Wallet:getByChain: ${error}`)
      return []
    }
  }

  /**
   * Get wallet by address
   * @param address Wallet address
   * @returns The wallet or null if not found
   */
  static async getByAddress(address: string) {
    try {
      const wallet = await db.select().from(tableWallets).where(eq(tableWallets.address, address)).limit(1)
      return wallet[0] || null
    } catch (error) {
      log.error(`Failed Wallet:getByAddress: ${error}`)
      return null
    }
  }

  /**
   * Calculate total balance for a user across all wallets for a specific chain
   * @param ownerId User ID
   * @param chain Blockchain network
   * @returns Total balance as a BigInt or null if operation failed
   */
  static async calculateTotalBalanceByChain(ownerId: number, chain: TypeChainName) {
    try {
      const wallets = await this.getByChain(ownerId, chain)

      // Calculate total balance for the specified chain
      let totalBalance = BigInt(0)
      for (const wallet of wallets) {
        totalBalance += wallet.balance
      }

      return totalBalance
    } catch (error) {
      log.error(`Failed Wallet:calculateTotalBalanceByChain: ${error}`)
      return null
    }
  }

  /**
   * Get wallet count by chain for a user
   * @param ownerId User ID
   * @returns Object with chain counts
   */
  static async getWalletCountsByChain(ownerId: number) {
    try {
      const wallets = await this.getAllForOwner(ownerId)
      const counts: Record<string, number> = {}

      // Initialize counts for all chains
      dataChainName.forEach((chain) => {
        counts[chain] = 0
      })

      // Count wallets for each chain
      for (const wallet of wallets) {
        counts[wallet.chain] = (counts[wallet.chain] || 0) + 1
      }

      return counts
    } catch (error) {
      log.error(`Failed Wallet:getWalletCountsByChain: ${error}`)
      return null
    }
  }

  /**
   * Get decrypted private key for a wallet (SECURE - with ownership validation)
   * @param walletId Wallet ID
   * @param ownerId User ID (owner of the wallet) - ensures user can only decrypt their own wallet keys
   * @param encryptionPassword Password to decrypt the private key
   * @returns Decrypted private key or null if decryption failed or wallet not owned by user
   */
  static async getDecryptedPrivateKey(walletId: number, ownerId: number, encryptionPassword: string) {
    try {
      const wallet = await this.getByIdForOwner(walletId, ownerId)
      if (!wallet) {
        return null
      }

      // Decrypt the private key
      const decryptedPrivateKey = BlockchainWallet.decryptPrivateKey(wallet.privateKey, encryptionPassword)
      return decryptedPrivateKey
    } catch (error) {
      log.error(`Failed Wallet:getDecryptedPrivateKey: ${error}`)
      return null
    }
  }
}
