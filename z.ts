import { db } from "./src/db/index"

// Get all tables and their data
const tables = await db.execute(
  `SELECT table_name 
   FROM information_schema.tables 
   WHERE table_schema = 'public'`
)

// Fetch data from each table
for (const table of (tables as any).rows) {
  const tableName = table.table_name
  console.log(`\n=== Data from ${tableName} ===`)
  const { rows }: any = await db.execute(`SELECT * FROM ${tableName}`)
  console.log(rows)
}
// console.log(tables)
