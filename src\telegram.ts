import { <PERSON><PERSON>, type <PERSON><PERSON>ontext, Context, InlineKeyboard } from "grammy"
import { log } from "./log"
import { Content } from "./content"
import { User, Wallet, TradingHistory, Fees } from "./models"
import { data<PERSON>hain<PERSON>ame, type <PERSON><PERSON>hainName } from "./data"
import { BlockchainWallet } from "./blockchain/wallet"
import { BlockchainConfig } from "./blockchain/config"
import { Keyv } from "keyv"

const content = new Content()
const cache = new Keyv({ store: new Map() })
export class Telegram {
  private passwordWallet = process.env.PASSWORD_WALLET as string
  private bot = new Bot(process.env.TELEGRAM_TOKEN as string)

  constructor() {
    // Register callback query handlers for inline keyboards
    this.bot.on("callback_query:data", this.handleCallbackQuery.bind(this))

    // Handle any text message to show main menu (entry point for new users)
    this.bot.on("message:text", this.handleTextMessage.bind(this))

    // Handled error
    this.bot.catch((err) => {
      log.error(JSON.stringify(err))
    })

    // Started bot
    this.bot.start({
      onStart(botInfo) {
        log.info(`Telegram bot started: ${botInfo.username}`)
      }
    })
  }

  /**
   * Session Management Methods
   */
  /**
   * Store session data for a user
   * @param userId Telegram user ID
   * @param sessionData Session data object containing state and temporary data
   */
  private async sessionSet(userId: number, sessionData: Record<string, any>): Promise<void> {
    try {
      await cache.set(`session:${userId}`, sessionData)
    } catch (error) {
      log.error(`Error sessionSet: ${error}`)
    }
  }

  /**
   * Retrieve session data for a user
   * @param userId Telegram user ID
   * @returns Session data object or null if no active session
   */
  private async sessionGet(userId: number): Promise<Record<string, any> | null> {
    try {
      return (await cache.get(`session:${userId}`)) || null
    } catch (error) {
      log.error(`Error sessionGet: ${error}`)
      return null
    }
  }

  /**
   * Clear session data when operation completes
   * @param userId Telegram user ID
   */
  private async sessionDelete(userId: number): Promise<void> {
    try {
      await cache.delete(`session:${userId}`)
    } catch (error) {
      log.error(`Error sessionDelete: ${error}`)
    }
  }

  /**
   * Check if user has an active input session
   * @param userId Telegram user ID
   * @returns True if user has an active session
   */
  private async sessionHas(userId: number): Promise<boolean> {
    try {
      return await cache.has(`session:${userId}`)
    } catch (error) {
      log.error(`Error sessionHas: ${error}`)
      return false
    }
  }

  /**
   * Reusable helper method to reply with content
   * @param ctx Telegram context object
   * @param contentKey Content key to retrieve from content system
   * @param data Optional data object for variable replacement
   * @param options Optional reply options
   * @returns Promise from ctx.reply()
   */
  private async reply(ctx: CommandContext<Context>, contentKey: string, data?: Record<string, any>, options?: any): Promise<any> {
    return ctx.reply(content.get(contentKey, data), { parse_mode: `HTML`, ...options })
  }

  /**
   * Send message with inline keyboard and track state
   * @param ctx Telegram context object
   * @param contentKey Content key to retrieve from content system
   * @param keyboard Inline keyboard markup
   * @param keyboardType Type identifier for the keyboard
   * @param data Optional data object for variable replacement
   * @param keyboardData Optional data to store with keyboard state
   * @returns Promise from ctx.reply()
   */
  private async replyWithKeyboard(ctx: CommandContext<Context>, contentKey: string, keyboard: InlineKeyboard, data?: Record<string, any>): Promise<any> {
    try {
      const message = await ctx.reply(content.get(contentKey, data), {
        parse_mode: `HTML`,
        reply_markup: keyboard
      })

      return message
    } catch (error) {
      log.error(`Error replyWithKeyboard: ${error}`)
      return this.reply(ctx, contentKey, data)
    }
  }

  /**
   * Update existing inline keyboard or delete if update fails
   * @param ctx Callback query context
   * @param contentKey Content key for new message text
   * @param keyboard New inline keyboard markup
   * @param data Optional data object for variable replacement
   * @returns Promise indicating success
   */
  private async updateKeyboard(ctx: any, contentKey: string, keyboard: InlineKeyboard, data?: Record<string, any>) {
    try {
      await ctx.editMessageText(content.get(contentKey, data), {
        parse_mode: `HTML`,
        reply_markup: keyboard
      })
    } catch (error) {
      await this.replyWithKeyboard(ctx, contentKey, keyboard, data)
    }
  }

  /**
   * Delete inline keyboard message
   * @param ctx Callback query context
   * @returns Promise indicating success
   */
  private async deleteKeyboard(ctx: any) {
    try {
      await ctx.deleteMessage()
    } catch (error) {
      log.error(`Error deleteKeyboard: ${error}`)
    }
  }

  /**
   * Answer callback query with content
   * @param ctx Callback query context
   * @param contentKey Content key to retrieve from content system
   * @param data Optional data object for variable replacement
   * @returns Promise from ctx.answerCallbackQuery()
   */
  private async answerCallback(ctx: any, contentKey: string, data?: Record<string, any>): Promise<void> {
    try {
      await ctx.answerCallbackQuery(content.get(contentKey, data))
    } catch (error) {
      log.error(`Error answerCallback: ${error}`)
      // Fallback to empty answer to prevent timeout
      try {
        await ctx.answerCallbackQuery()
      } catch (fallbackError) {
        log.error(`Error answerCallback with fallback: ${fallbackError}`)
      }
    }
  }

  /**
   * Handle callback queries from inline keyboards
   * @param ctx Callback query context
   */
  private async handleCallbackQuery(ctx: any): Promise<void> {
    try {
      await ctx.answerCallbackQuery()

      const data = ctx.callbackQuery.data
      const [action, ...params] = data.split(":")

      switch (action) {
        case "wallet_action":
          await this.handleWalletAction(ctx, params)
          break
        case "wallet_select":
          await this.handleWalletSelect(ctx, params)
          break
        case "wallet_detail":
          await this.handleWalletDetail(ctx, params)
          break
        case "wallet_delete_select":
          await this.handleWalletDeleteSelect(ctx, params)
          break
        case "chain_select":
          await this.handleChainSelect(ctx, params)
          break
        case "chain_detail":
          await this.handleChainDetail(ctx, params)
          break
        case "confirm_delete":
          await this.handleConfirmDelete(ctx, params)
          break
        case "export_confirm":
          await this.handleExportConfirm(ctx, params)
          break
        case "create_wallet":
          await this.handleCreateWalletChain(ctx, params)
          break
        case "import_chain":
          await this.handleImportChain(ctx, params)
          break
        case "help":
          await this.handleHelpSection(ctx, params)
          break
        case "settings":
          await this.handleSettingsAction(ctx, params)
          break
        case "cancel":
          await this.handleCancel(ctx)
          break
        case "back":
          await this.handleBack(ctx, params)
          break
        case "page":
          await this.handlePagination(ctx, params)
          break
        case "noop":
          // No-operation: completely non-interactive button
          // Do nothing - no acknowledgment, no response, no UI changes
          break
        case "main_menu":
          await this.showMainMenu(ctx)
          break
        default:
          log.warn(`Unknown callback action: ${action}`)
          await this.deleteKeyboard(ctx)
      }
    } catch (error) {
      log.error(`Error handleCallbackQuery: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle text messages - check for active sessions or show main menu
   * @param ctx Message context from Grammy
   */
  private async handleTextMessage(ctx: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      const user = await User.getOrCreate(telegramId, ctx.from?.username || `user_${telegramId}`, ctx.from?.first_name || "Unknown")

      if (!user) {
        const keyboard = new InlineKeyboard().text("🔄 Try Again", "main_menu")
        await this.replyWithKeyboard(ctx, "user_creation_failed", keyboard)
        return
      }

      // Check if user has an active session
      const hasSession = await this.sessionHas(telegramId)
      if (hasSession) {
        await this.handleSessionInput(ctx, telegramId)
        return
      }

      // No active session - show main menu
      await this.showMainMenu(ctx)
    } catch (error) {
      log.error(`Error handleTextMessage: ${error}`)
      const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await this.replyWithKeyboard(ctx, "general_error", keyboard)
    }
  }

  /**
   * Handle user text input based on active session state
   * @param ctx Message context from Grammy
   * @param userId Telegram user ID
   */
  private async handleSessionInput(ctx: any, userId: number): Promise<void> {
    try {
      const session = await this.sessionGet(userId)
      if (!session || !session.state) {
        await this.sessionDelete(userId)
        await this.showMainMenu(ctx)
        return
      }

      const userInput = ctx.message?.text?.trim()
      if (!userInput) {
        const keyboard = new InlineKeyboard().text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "input_invalid_empty", keyboard)
        return
      }

      switch (session.state) {
        case "waiting_wallet_name":
          await this.processWalletNameInput(ctx, userId, userInput, session)
          break
        case "waiting_private_key":
          await this.processPrivateKeyInput(ctx, userId, userInput, session)
          break
        case "waiting_import_details":
          await this.processImportDetailsInput(ctx, userId, userInput, session)
          break
        default:
          log.warn(`Unknown session state: ${session.state}`)
          await this.sessionDelete(userId)
          await this.showMainMenu(ctx)
      }
    } catch (error) {
      log.error(`Error handleSessionInput: ${error}`)
      await this.sessionDelete(userId)
      const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await this.replyWithKeyboard(ctx, "session_error", keyboard)
    }
  }

  /**
   * Handle wallet action callbacks
   * @param ctx Callback query context
   * @param params Action parameters
   */
  private async handleWalletAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    switch (action) {
      case "list":
        await this.showWalletList(ctx)
        break
      case "create":
        await this.showCreateWalletForm(ctx)
        break
      case "import":
        await this.showImportWalletForm(ctx)
        break
      case "stats":
        await this.showUserStats(ctx)
        break
      case "settings":
        await this.showSettings(ctx)
        break
      case "help":
        await this.showHelpMenu(ctx)
        break
      case "view":
        await this.handleWalletSelect(ctx, [params[1] as string])
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet selection callbacks
   * @param ctx Callback query context
   * @param params Selection parameters
   */
  private async handleWalletSelect(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0] as any)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    // Get user ID for ownership validation
    const telegramId = ctx.from?.id || 0
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    // Show wallet details with action buttons
    const keyboard = new InlineKeyboard()
      .text("💰 Balance", `wallet_detail:balance:${walletId}`)
      .text("📊 History", `wallet_detail:history:${walletId}`)
      .row()
      .text("📈 Stats", `wallet_detail:stats:${walletId}`)
      .text("🔓 Export Key", `wallet_detail:export:${walletId}`)
      .row()
      .text("🗑️ Delete", `wallet_detail:delete:${walletId}`)
      .text("🔙 Back to Wallets", "wallet_action:list")

    const { symbol, name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]
    const balance = wallet.balance.toString()
    const createdDate = wallet.createdAt?.toLocaleDateString() || "Unknown"

    await this.updateKeyboard(ctx, "wallet_info", keyboard, {
      name: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address,
      balance,
      symbol,
      chainId: wallet.chainId,
      createdDate,
      createdBy: wallet.createdBy
    })
  }

  /**
   * Handle chain selection callbacks
   * @param ctx Callback query context
   * @param params Chain parameters
   */
  private async handleChainSelect(ctx: any, params: string[]): Promise<void> {
    const chain = params[0] as TypeChainName
    const action = params[1] || "info"

    if (action === "info") {
      const { chainId, symbol, name } = BlockchainConfig[chain]
      const keyboard = new InlineKeyboard().text("💰 View Fees", `chain_detail:fees:${chain}`).text("📊 Network Stats", `chain_detail:stats:${chain}`).row().text("🔙 Back to Menu", "back:main_menu")
      await this.updateKeyboard(ctx, "chain_info", keyboard, {
        chainName: name,
        symbol,
        chainId,
        chain
      })
    }
  }

  /**
   * Handle delete confirmation callbacks
   * @param ctx Callback query context
   * @param params Delete parameters
   */
  private async handleConfirmDelete(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0] as any)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    const telegramId = ctx.from?.id || 0
    const user = await User.getById(telegramId)

    if (!user) {
      await this.answerCallback(ctx, "callback_user_not_found")
      await this.deleteKeyboard(ctx)
      return
    }

    // Use secure method to get wallet with ownership validation
    const wallet = await Wallet.getByIdForOwner(walletId, user.id)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    const success = await Wallet.remove(walletId, user.id)

    if (success) {
      await this.answerCallback(ctx, "callback_wallet_deleted_success")
      await this.showWalletList(ctx)
    } else {
      await this.answerCallback(ctx, "callback_wallet_delete_failed")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle cancel callbacks
   * @param ctx Callback query context
   */
  private async handleCancel(ctx: any): Promise<void> {
    const userId = ctx.from?.id as number

    // Clear any active session
    await this.sessionDelete(userId)

    await this.answerCallback(ctx, "callback_cancelled")
    await this.deleteKeyboard(ctx)

    // Show main menu after cancellation
    await this.showMainMenu(ctx)
  }

  /**
   * Handle back navigation callbacks
   * @param ctx Callback query context
   * @param params Navigation parameters
   */
  private async handleBack(ctx: any, params: string[]): Promise<void> {
    const destination = params[0]

    switch (destination) {
      case "main_menu":
        await this.showMainMenu(ctx)
        break
      case "wallets":
        await this.showWalletList(ctx)
        break
      case "help":
        await this.showHelpMenu(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle pagination callbacks
   * @param ctx Callback query context
   * @param params Pagination parameters
   */
  private async handlePagination(ctx: any, params: string[]): Promise<void> {
    const type = params[0]
    const page = parseInt(params[1] as any)

    if (isNaN(page)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (type) {
      case "wallets":
        await this.showWalletListPage(ctx, page)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show main menu with inline keyboard
   * @param ctx Callback query context
   */
  private async showMainMenu(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("💼 My Wallets", "wallet_action:list").text("📊 View Stats", "wallet_action:stats").row().text("⚙️ Settings", "wallet_action:settings").text("❓ Help", "wallet_action:help")

    await this.updateKeyboard(ctx, "start", keyboard)
  }

  /**
   * Show wallet list with inline keyboard
   * @param ctx Callback query context
   */
  private async showWalletList(ctx: any): Promise<void> {
    await this.showWalletListPage(ctx, 0)
  }

  /**
   * Show wallet list page with pagination
   * @param ctx Callback query context
   * @param page Page number (0-based)
   */
  private async showWalletListPage(ctx: any, page: number): Promise<void> {
    try {
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.answerCallback(ctx, "callback_user_not_found")
        await this.deleteKeyboard(ctx)
        return
      }

      const wallets = await Wallet.getAllForOwner(user.id)

      if (wallets.length === 0) {
        const keyboard = new InlineKeyboard().text("➕ Create Wallet", "wallet_action:create").text("📥 Import Wallet", "wallet_action:import").row().text("🔙 Back to Menu", "back:main_menu")

        await this.updateKeyboard(ctx, "wallets_empty", keyboard)
        return
      }

      // Create inline keyboard for wallet selection
      const keyboard = new InlineKeyboard()

      // Add wallet buttons (max 5 per page for better UX)
      const walletsPerPage = 5
      const totalPages = Math.ceil(wallets.length / walletsPerPage)
      const currentPage = Math.max(0, Math.min(page, totalPages - 1))
      const startIndex = currentPage * walletsPerPage
      const endIndex = Math.min(startIndex + walletsPerPage, wallets.length)

      for (let i = startIndex; i < endIndex; i++) {
        const wallet = wallets[i] as any
        const balance = wallet.balance.toString()
        const { symbol } = BlockchainConfig[wallet.chain as TypeChainName]
        keyboard.text(`💼 ${wallet.name} (${balance} ${symbol})`, `wallet_select:${wallet.id}`).row()
      }

      // Add pagination if needed
      if (totalPages > 1) {
        keyboard.row()
        if (currentPage > 0) {
          keyboard.text("⬅️ Previous", `page:wallets:${currentPage - 1}`)
        }
        keyboard.text(`${currentPage + 1}/${totalPages}`, "noop")
        if (currentPage < totalPages - 1) {
          keyboard.text("➡️ Next", `page:wallets:${currentPage + 1}`)
        }
      }

      // Add action buttons
      keyboard.row().text("➕ Create", "wallet_action:create").text("📥 Import", "wallet_action:import").text("🔙 Menu", "back:main_menu")

      // Format wallet list for display
      const walletList = wallets
        .slice(startIndex, endIndex)
        .map((wallet, index) => {
          const balance = wallet.balance.toString()
          const { symbol } = BlockchainConfig[wallet.chain as TypeChainName]
          return `${startIndex + index + 1}. ${wallet.name}\n   🔗 ${wallet.chain}\n   💰 ${balance} ${symbol}\n   📍 ${wallet.address}`
        })
        .join("\n\n")

      await this.updateKeyboard(ctx, "wallets_list", keyboard, {
        walletList,
        totalWallets: wallets.length,
        currentPage: currentPage + 1,
        totalPages
      })
    } catch (error) {
      log.error(`Error showWalletListPage: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Add chain selection buttons to keyboard dynamically
   * @param keyboard InlineKeyboard instance to add buttons to
   * @param callbackPrefix Prefix for callback data (e.g., "create_wallet" or "import_chain")
   * @param maxChains Maximum number of chains to show (default: 6)
   */
  private addChainButtons(keyboard: InlineKeyboard, callbackPrefix: string, maxChains: number = 6): void {
    const chains = dataChainName.slice(0, maxChains) as TypeChainName[]
    for (let i = 0; i < chains.length; i += 2) {
      keyboard.row()
      const chain1 = chains[i]
      const chain2 = chains[i + 1]

      // Add first chain button
      if (chain1 && BlockchainConfig[chain1]) {
        const config1 = BlockchainConfig[chain1]
        keyboard.text(`🔗 ${config1.name}`, `${callbackPrefix}:${chain1}`)
      }

      // Add second chain button if exists
      if (chain2 && BlockchainConfig[chain2]) {
        const config2 = BlockchainConfig[chain2]
        keyboard.text(`🔗 ${config2.name}`, `${callbackPrefix}:${chain2}`)
      }
    }
  }

  /**
   * Show create wallet form
   * @param ctx Callback query context
   */
  private async showCreateWalletForm(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()

    // Add chain selection buttons dynamically
    this.addChainButtons(keyboard, "create_wallet")
    keyboard.row().text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "createwallet_usage", keyboard)
  }

  /**
   * Show import wallet form - directly show network selection
   * @param ctx Callback query context
   */
  private async showImportWalletForm(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()

    // Add chain selection buttons dynamically
    this.addChainButtons(keyboard, "import_chain")
    keyboard.row().text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "importwallet_usage", keyboard)
  }

  /**
   * Show comprehensive trading analytics directly
   * @param ctx Callback query context
   */
  private async showUserStats(ctx: any): Promise<void> {
    // Directly call consolidated stats display
    await this.handleStatsDisplay(ctx)
  }

  /**
   * Show settings menu
   * @param ctx Callback query context
   */
  private async showSettings(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("🌐 Language", "settings:language").text("🔔 Notifications", "settings:notifications").row().text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "settings_menu", keyboard)
  }

  /**
   * Show help menu
   * @param ctx Callback query context
   */
  private async showHelpMenu(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("💼 Wallet Commands", "help:wallets").text("📊 Trading Commands", "help:trading").row().text("⚙️ Config Commands", "help:config").text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "help", keyboard)
  }

  /**
   * Handle wallet detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleWalletDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const walletIdStr = params[1]
    if (!walletIdStr) {
      await this.deleteKeyboard(ctx)
      return
    }

    const walletId = parseInt(walletIdStr)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    // Get user ID for ownership validation
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    switch (action) {
      case "balance":
        await this.showWalletBalance(ctx, wallet)
        break
      case "history":
        await this.showWalletHistory(ctx, wallet)
        break
      case "stats":
        await this.showWalletStats(ctx, wallet)
        break
      case "export":
        await this.showExportConfirmation(ctx, wallet)
        break
      case "delete":
        await this.showDeleteConfirmation(ctx, wallet)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet delete selection
   * @param ctx Callback query context
   * @param params Selection parameters
   */
  private async handleWalletDeleteSelect(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0] as any)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    // Get user ID for ownership validation
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    await this.showDeleteConfirmation(ctx, wallet)
  }

  /**
   * Handle export confirmation
   * @param ctx Callback query context
   * @param params Export parameters
   */
  private async handleExportConfirm(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0] as any)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    // Get user ID for ownership validation
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    await this.showPrivateKey(ctx, wallet)
  }

  /**
   * Handle chain detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleChainDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const chain = params[1] as TypeChainName

    switch (action) {
      case "stats":
        await this.showChainStats(ctx, chain)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle create wallet chain selection - directly prompt for wallet name
   * @param ctx Callback query context
   * @param params Chain parameters
   */
  private async handleCreateWalletChain(ctx: any, params: string[]): Promise<void> {
    const chain = params[0] as TypeChainName
    const userId = ctx.from?.id as number

    // Set up session to wait for wallet name input directly
    await this.sessionSet(userId, {
      state: "waiting_wallet_name",
      chain: chain,
      operation: "create_wallet"
    })

    const keyboard = new InlineKeyboard().text("🔙 Back to Chains", "wallet_action:create").text("❌ Cancel", "cancel")

    const { symbol, name } = BlockchainConfig[chain]
    await this.updateKeyboard(ctx, "createwallet_name_prompt", keyboard, {
      chainName: name,
      symbol
    })
  }

  /**
   * Handle import chain selection - directly prompt for wallet name
   * @param ctx Callback query context
   * @param params Chain parameters
   */
  private async handleImportChain(ctx: any, params: string[]): Promise<void> {
    const chain = params[0] as TypeChainName
    const userId = ctx.from?.id as number

    // Set up session to wait for wallet name input with selected chain
    await this.sessionSet(userId, {
      state: "waiting_import_details",
      operation: "import_wallet",
      chain: chain,
      step: "wallet_name"
    })

    const { symbol, name } = BlockchainConfig[chain]
    const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
    await this.updateKeyboard(ctx, "importwallet_name_prompt", keyboard, {
      chainName: name,
      symbol
    })
  }

  /**
   * Handle help section selection
   * @param ctx Callback query context
   * @param params Help parameters
   */
  private async handleHelpSection(ctx: any, params: string[]): Promise<void> {
    const section = params[0]

    const keyboard = new InlineKeyboard().text("🔙 Back to Help", "wallet_action:help").text("🏠 Main Menu", "back:main_menu")
    switch (section) {
      case "wallets":
        await this.updateKeyboard(ctx, "help_wallets", keyboard)
        break
      case "trading":
        await this.updateKeyboard(ctx, "help_trading", keyboard)
        break
      case "config":
        await this.updateKeyboard(ctx, "help_config", keyboard)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle settings actions
   * @param ctx Callback query context
   * @param params Settings parameters
   */
  private async handleSettingsAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]

    const keyboard = new InlineKeyboard().text("🔙 Back to Settings", "wallet_action:settings").text("🏠 Main Menu", "back:main_menu")
    switch (action) {
      case "language":
        await this.updateKeyboard(ctx, "settings_language", keyboard)
        break
      case "notifications":
        await this.updateKeyboard(ctx, "settings_notifications", keyboard)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle comprehensive statistics display
   * @param ctx Callback query context
   */
  private async handleStatsDisplay(ctx: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      // const user = await User.getById(telegramId)

      const wallets = await Wallet.getAllForOwner(telegramId)
      const totalWallets = wallets.length

      // Calculate comprehensive trading statistics across all wallets
      let totalTrades = 0
      let totalSuccessful = 0
      let totalFailed = 0
      let totalVolume = BigInt(0)
      let totalSuccessVolume = BigInt(0)
      let totalBuyTrades = 0
      let totalSellTrades = 0

      // Get statistics for each chain
      const chainStatsArray: any[] = []

      for (const chain of dataChainName) {
        const stats = await TradingHistory.getChainStats(chain)
        if (stats && stats.totalTrades > 0) {
          chainStatsArray.push({
            chainName: chain,
            ...stats,
            successRate: ((stats.successTrades / stats.totalTrades) * 100).toFixed(2)
          })

          // Add to overall totals
          totalTrades += stats.totalTrades
          totalSuccessful += stats.successTrades
          totalFailed += stats.failedTrades
          totalVolume += stats.totalVolume
          totalSuccessVolume += stats.successVolume
          totalBuyTrades += stats.buyTrades
          totalSellTrades += stats.sellTrades
        }
      }

      const overallSuccessRate = totalTrades > 0 ? ((totalSuccessful / totalTrades) * 100).toFixed(2) : "0"
      const activeChains = chainStatsArray.length

      // Create network performance summary string
      const networkPerformance =
        chainStatsArray.length > 0 ? chainStatsArray.map(({ chainName, totalTrades, successRate, uniqueWallets }) => `• <b>${chainName}:</b> ${totalTrades} trades | ${successRate}% success | ${uniqueWallets} wallets`).join("\n") : "• No trading activity found across networks"

      // Create buy/sell ratio string
      const buyToSellRatio = totalSellTrades > 0 ? `${totalBuyTrades}:${totalSellTrades}` : totalBuyTrades > 0 ? "All Buy Orders" : "No Trades"

      const keyboard = new InlineKeyboard().text("🔙 Back to Menu", "back:main_menu")

      // Create portfolio status message
      const portfolioStatus =
        totalWallets > 0
          ? `• Managing ${totalWallets} wallet(s) across ${activeChains} network(s)\n• Real-time balance tracking and monitoring\n• Multi-chain portfolio diversification`
          : "• No wallets found - create your first wallet to start trading\n• Import existing wallets to manage your portfolio\n• Start building your multi-chain trading strategy"

      // Create trading insights
      const successRateNum = parseFloat(overallSuccessRate)
      const tradingInsights =
        totalTrades > 0
          ? `• Success rate of ${overallSuccessRate}% indicates ${successRateNum >= 70 ? "strong" : successRateNum >= 50 ? "moderate" : "developing"} trading performance\n• ${
              totalBuyTrades > totalSellTrades ? "Buy-focused" : totalSellTrades > totalBuyTrades ? "Sell-focused" : "Balanced"
            } trading strategy detected\n• Active across ${activeChains} blockchain network(s)\n• Total trading volume: ${totalVolume.toString()}`
          : "• No trading activity detected yet\n• Start executing trades to see performance analytics\n• Monitor success rates and optimize your strategy\n• Track volume and profitability across networks"

      await this.updateKeyboard(ctx, "stats_display", keyboard, {
        totalWallets,
        totalTrades,
        totalSuccessful,
        totalFailed,
        overallSuccessRate,
        totalVolume: totalVolume.toString(),
        totalSuccessVolume: totalSuccessVolume.toString(),
        totalBuyTrades,
        totalSellTrades,
        activeChains,
        networkPerformance,
        buyToSellRatio,
        portfolioStatus,
        tradingInsights,
        username: ctx.from?.username || "Unknown"
      })
    } catch (error) {
      log.error(`Error handleStatsDisplay: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show wallet balance details
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showWalletBalance(ctx: any, wallet: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

    const balance = wallet.balance.toString()
    const { symbol } = BlockchainConfig[wallet.chain as TypeChainName]
    await this.updateKeyboard(ctx, "balance_display", keyboard, {
      walletName: wallet.name,
      balance,
      symbol,
      chainName: wallet.chain
    })
  }

  /**
   * Show wallet trading history
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showWalletHistory(ctx: any, wallet: any): Promise<void> {
    try {
      const history = await TradingHistory.getForWallet(BigInt(wallet.id), 10)

      const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

      if (history.length === 0) {
        await this.updateKeyboard(ctx, "history_empty", keyboard, { walletName: wallet.name })
        return
      }

      const historyText = history
        .map((trade, index) => {
          const operation = trade.operation === "B" ? "🟢 BUY" : "🔴 SELL"
          const status = trade.success ? "✅" : "❌"
          const amount = trade.amount.toString()
          const date = trade.createdAt?.toLocaleDateString() || "Unknown"
          const { symbol } = BlockchainConfig[trade.chain as TypeChainName]

          return `${index + 1}. ${operation} ${status}\n   💰 ${amount} ${symbol}\n   📅 ${date}`
        })
        .join("\n\n")

      await this.updateKeyboard(ctx, "history_display", keyboard, {
        walletName: wallet.name,
        historyText
      })
    } catch (error) {
      log.error(`Error showWalletHistory: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show wallet statistics
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showWalletStats(ctx: any, wallet: any): Promise<void> {
    try {
      const stats = await TradingHistory.getWalletStats(BigInt(wallet.id))

      const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)
      if (!stats) {
        await this.updateKeyboard(ctx, "stats_empty", keyboard, { walletName: wallet.name })
        return
      }

      const successRate = stats.totalTrades > 0 ? ((stats.successTrades / stats.totalTrades) * 100).toFixed(2) : "0"
      const { symbol } = BlockchainConfig[wallet.chain as TypeChainName]

      await this.updateKeyboard(ctx, "stats_display", keyboard, {
        walletName: wallet.name,
        totalTrades: stats.totalTrades,
        successTrades: stats.successTrades,
        failedTrades: stats.failedTrades,
        successRate,
        totalVolume: stats.totalVolume.toString(),
        successVolume: stats.successVolume.toString(),
        symbol,
        buyTrades: stats.buyTrades,
        sellTrades: stats.sellTrades
      })
    } catch (error) {
      log.error(`Error showWalletStats: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show export confirmation with security warning
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showExportConfirmation(ctx: any, wallet: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("🔑 Yes, Export Key", `export_confirm:${wallet.id}`).text("🔙 Cancel", `wallet_select:${wallet.id}`)

    const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]
    await this.updateKeyboard(ctx, "export_confirmation", keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address
    })
  }

  /**
   * Show private key with security warnings
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showPrivateKey(ctx: any, wallet: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      const privateKey = await Wallet.getDecryptedPrivateKey(wallet.id, telegramId, this.passwordWallet)

      if (!privateKey) {
        // Show error if decryption failed or user doesn't own wallet
        const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)
        const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]

        await this.updateKeyboard(ctx, "export_error", keyboard, {
          walletName: wallet.name,
          chainName: chainDisplayName,
          address: wallet.address
        })
        return
      }

      // Show the private key with security warnings
      const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)
      const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]

      await this.updateKeyboard(ctx, "export_private_key", keyboard, {
        walletName: wallet.name,
        chainName: chainDisplayName,
        address: wallet.address,
        privateKey
      })
    } catch (error) {
      log.error(`Error showPrivateKey: ${error}`)

      // Show error message
      const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)
      const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]

      await this.updateKeyboard(ctx, "export_error", keyboard, {
        walletName: wallet.name,
        chainName: chainDisplayName,
        address: wallet.address
      })
    }
  }

  /**
   * Show delete confirmation dialog
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showDeleteConfirmation(ctx: any, wallet: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("✅ Yes, Delete", `confirm_delete:${wallet.id}`).text("❌ Cancel", "cancel").row().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

    const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]
    await this.updateKeyboard(ctx, "deletewallet_confirm", keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address
    })
  }

  /**
   * Show chain statistics
   * @param ctx Callback query context
   * @param chain Chain name
   */
  private async showChainStats(ctx: any, chain: TypeChainName): Promise<void> {
    const keyboard = new InlineKeyboard().text("💰 View Fees", `chain_detail:fees:${chain}`).text("🔙 Back to Chain", `chain_select:${chain}:info`)

    const { chainId, symbol, name } = BlockchainConfig[chain]

    // For now, show basic chain info as stats
    await this.updateKeyboard(ctx, "chain_info", keyboard, {
      chainName: name,
      symbol,
      chainId,
      chain
    })
  }

  /**
   * Process wallet name input from user
   * @param ctx Message context
   * @param userId Telegram user ID
   * @param walletName User input for wallet name
   * @param session Current session data
   */
  private async processWalletNameInput(ctx: any, userId: number, walletName: string, session: Record<string, any>): Promise<void> {
    try {
      // Validate wallet name
      if (walletName.length > 32) {
        const keyboard = new InlineKeyboard().text("🔙 Back to Chains", "wallet_action:create").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_too_long", keyboard)
        return
      }

      if (!/^[a-zA-Z0-9_-]+$/.test(walletName)) {
        const keyboard = new InlineKeyboard().text("🔙 Back to Chains", "wallet_action:create").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_invalid_chars", keyboard)
        return
      }

      // Check if wallet name already exists for this user
      const user = await User.getById(userId)
      if (!user) {
        await this.sessionDelete(userId)
        const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
        await this.replyWithKeyboard(ctx, "user_not_found", keyboard)
        return
      }

      const existingWallet = await Wallet.getByName(user.id, walletName)
      if (existingWallet) {
        const keyboard = new InlineKeyboard().text("🔙 Back to Chains", "wallet_action:create").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_exists", keyboard)
        return
      }

      // Generate wallet address and private key
      const chain = session.chain as TypeChainName
      const { address, privateKey } = new BlockchainWallet(chain).generate()

      // Get chain ID from blockchain config
      const { chainId } = BlockchainConfig[chain]

      // Create the wallet with encryption
      const wallet = await Wallet.create(user.id, walletName, chain, chainId, address, privateKey, "system", this.passwordWallet)

      if (wallet && typeof wallet === "object") {
        // Success - wallet created
        await this.sessionDelete(userId)

        const successKeyboard = new InlineKeyboard().text("👁️ View Wallet", `wallet_action:view:${wallet.id}`).text("➕ Create Another", `create_wallet:${chain}`).row().text("🏠 Back to Main Menu", "main_menu")
        const { name: chainDisplayName } = BlockchainConfig[chain]
        await this.replyWithKeyboard(ctx, "createwallet_success", successKeyboard, {
          walletName: wallet.name,
          chainName: chainDisplayName,
          address: wallet.address
        })
      } else if (wallet === "DUPLICATE_NAME") {
        // Wallet name already exists
        const keyboard = new InlineKeyboard().text("🔙 Back to Chains", "wallet_action:create").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_exists", keyboard)
      } else if (wallet === "DUPLICATE_ADDRESS") {
        // This is extremely unlikely for generated wallets, but handle it for completeness
        const keyboard = new InlineKeyboard().text("🔄 Try Again", `create_wallet:${chain}`).text("🏠 Back to Main Menu", "main_menu")
        const { name: chainDisplayName } = BlockchainConfig[chain]
        await this.replyWithKeyboard(ctx, "importwallet_duplicate_address", keyboard, {
          chainName: chainDisplayName,
          address: address
        })
      } else {
        // General creation failure
        await this.sessionDelete(userId)

        const failureKeyboard = new InlineKeyboard().text("🔄 Try Again", `create_wallet:${chain}`).text("🏠 Back to Main Menu", "main_menu")
        await this.replyWithKeyboard(ctx, "createwallet_failed", failureKeyboard)
      }
    } catch (error) {
      log.error(`Error processWalletNameInput: ${error}`)
      await this.sessionDelete(userId)

      const errorKeyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")

      await this.replyWithKeyboard(ctx, "createwallet_error", errorKeyboard)
    }
  }

  /**
   * Process private key input from user
   * @param ctx Message context
   * @param userId Telegram user ID
   * @param privateKey User input for private key
   * @param session Current session data
   */
  private async processPrivateKeyInput(ctx: any, userId: number, privateKey: string, session: Record<string, any>): Promise<void> {
    try {
      // Validate private key format (basic validation)
      if (privateKey.length < 32) {
        const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "private_key_invalid", keyboard)
        return
      }

      // Process import with collected data
      const walletName = session.walletName
      const chain = session.chain as TypeChainName

      const user = await User.getById(userId)
      if (!user) {
        await this.sessionDelete(userId)
        const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
        await this.replyWithKeyboard(ctx, "user_not_found", keyboard)
        return
      }

      // Derive address from private key
      const address = await new BlockchainWallet(chain).getAddress(privateKey)

      if (!address) {
        const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "private_key_invalid", keyboard)
        return
      }

      // Get chain ID from blockchain config
      const { chainId } = BlockchainConfig[chain]

      const wallet = await Wallet.import(user.id, walletName, chain, chainId, address, privateKey, this.passwordWallet)

      if (wallet && typeof wallet === "object") {
        // Success - wallet imported
        await this.sessionDelete(userId)

        const successKeyboard = new InlineKeyboard().text("👁️ View Wallet", `wallet_action:view:${wallet.id}`).text("📥 Import Another", "wallet_action:import").row().text("🏠 Back to Main Menu", "main_menu")
        const { name: chainDisplayName } = BlockchainConfig[chain]
        await this.replyWithKeyboard(ctx, "importwallet_success", successKeyboard, {
          walletName: wallet.name,
          chainName: chainDisplayName,
          address: wallet.address
        })
      } else if (wallet === "DUPLICATE_NAME") {
        // Wallet name already exists
        const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_exists", keyboard)
      } else if (wallet === "DUPLICATE_ADDRESS") {
        // Wallet address already exists for this chain
        const keyboard = new InlineKeyboard().text("💼 View My Wallets", "wallet_action:list").text("📥 Import Different Wallet", "wallet_action:import").row().text("🏠 Back to Main Menu", "main_menu")
        const { name: chainDisplayName } = BlockchainConfig[chain]
        await this.replyWithKeyboard(ctx, "importwallet_duplicate_address", keyboard, {
          chainName: chainDisplayName,
          address: address
        })
      } else {
        // General import failure
        await this.sessionDelete(userId)

        const failureKeyboard = new InlineKeyboard().text("🔄 Try Again", "wallet_action:import").text("🏠 Back to Main Menu", "main_menu")

        await this.replyWithKeyboard(ctx, "importwallet_failed", failureKeyboard)
      }
    } catch (error) {
      log.error(`Error processPrivateKeyInput: ${error}`)
      await this.sessionDelete(userId)

      const errorKeyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")

      await this.replyWithKeyboard(ctx, "importwallet_error", errorKeyboard)
    }
  }

  /**
   * Process import details input from user (multi-step)
   * @param ctx Message context
   * @param userId Telegram user ID
   * @param userInput User input
   * @param session Current session data
   */
  private async processImportDetailsInput(ctx: any, userId: number, userInput: string, session: Record<string, any>): Promise<void> {
    try {
      const step = session.step || "wallet_name"

      if (step === "wallet_name") {
        // Validate wallet name
        if (userInput.length > 32) {
          const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
          await this.replyWithKeyboard(ctx, "wallet_name_too_long", keyboard)
          return
        }

        if (!/^[a-zA-Z0-9_-]+$/.test(userInput)) {
          const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
          await this.replyWithKeyboard(ctx, "wallet_name_invalid_chars", keyboard)
          return
        }

        // Check if wallet name already exists
        const user = await User.getById(userId)
        if (!user) {
          await this.sessionDelete(userId)
          const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
          await this.replyWithKeyboard(ctx, "user_not_found", keyboard)
          return
        }

        const existingWallet = await Wallet.getByName(user.id, userInput)
        if (existingWallet) {
          const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
          await this.replyWithKeyboard(ctx, "wallet_name_exists", keyboard)
          return
        }

        // Update session with wallet name and move to private key step (chain already selected)
        await this.sessionSet(userId, {
          ...session,
          walletName: userInput,
          step: "private_key"
        })

        const chain = session.chain as TypeChainName
        const { name: chainDisplayName } = BlockchainConfig[chain]
        const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "import_private_key_prompt", keyboard, { chainName: chainDisplayName })
      } else if (step === "private_key") {
        // Process private key input
        await this.processPrivateKeyInput(ctx, userId, userInput, session)
      }
    } catch (error) {
      log.error(`Error processImportDetailsInput: ${error}`)
      await this.sessionDelete(userId)

      const errorKeyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")

      await this.replyWithKeyboard(ctx, "importwallet_error", errorKeyboard)
    }
  }
}
