import { eq } from "drizzle-orm"
import { db } from "../db"
import { log } from "../log"
import { tableUsers } from "../db/schema/users"
import { BlockchainUtils } from "../blockchain/utils"

/**
 * User model class for managing Telegram bot users
 *
 * This class provides methods for creating, retrieving, and updating user records
 * in the PostgreSQL database. Each user represents a Telegram user who interacts
 * with the trading bot.
 */

export class User {
  /**
   * Get a user by Telegram username
   * @param username Telegram username
   * @returns The user or null if not found
   */
  static async getByUsername(username: string) {
    try {
      const user = await db.select().from(tableUsers).where(eq(tableUsers.username, username)).limit(1)
      return user[0] || null
    } catch (error) {
      log.error(`Failed to get user by username: ${error}`)
      return null
    }
  }

  /**
   * Get a user by ID
   * @param id User ID
   * @returns The user or null if not found
   */
  static async getById(id: number) {
    try {
      const user = await db.select().from(tableUsers).where(eq(tableUsers.id, id)).limit(1)
      return user[0] || null
    } catch (error) {
      log.error(`Failed User:getById: ${error}`)
      return null
    }
  }

  /**
   * Create a new user
   * @param telegramId Telegram user ID
   * @param username Telegram username (without @ symbol)
   * @param fullname User's full name
   * @returns The created user or null if creation failed
   */
  static async create(telegramId: number, username: string, fullname: string) {
    try {
      // Check if user already exists
      const existingUser = await this.getById(telegramId)
      if (existingUser) {
        return existingUser
      }

      // Create new user with default language
      await db
        .insert(tableUsers)
        .values({
          id: telegramId,
          username: username || "",
          fullname: fullname || "",
          isActive: true,
          fee: "0",
          language: "en"
        })
        .execute()

      // Get the created user
      return this.getById(telegramId)
    } catch (error) {
      log.error(`Failed User:create: ${error}`)
      return null
    }
  }

  /**
   * Get or create a user
   * @param telegramId Telegram user ID
   * @param username Telegram username
   * @param fullname User's full name
   * @returns The user or null if operation failed
   */
  static async getOrCreate(telegramId: number, username: string, fullname: string) {
    const user = await this.getById(telegramId)
    if (user) {
      return user
    }
    return this.create(telegramId, username, fullname)
  }

  /**
   * Update user's active status
   * @param userId User ID
   * @param isActive Whether the user should be active
   * @returns Updated user or null if operation failed
   */
  static async updateActiveStatus(userId: number, isActive: boolean) {
    try {
      await db.update(tableUsers).set({ isActive }).where(eq(tableUsers.id, userId)).execute()

      return this.getById(userId)
    } catch (error) {
      log.error(`Failed User:updateActiveStatus: ${error}`)
      return null
    }
  }

  /**
   * Update user's fee percentage
   * @param userId User ID
   * @param fee Fee percentage as string (e.g., "0.3" for 0.3%)
   * @returns Updated user or null if operation failed
   */
  static async updateFee(userId: number, fee: string) {
    try {
      // Validate fee range
      const feeNum = parseFloat(fee)
      if (isNaN(feeNum) || feeNum < 0 || feeNum > 100) {
        return null // Invalid fee percentage
      }
      const validatedFee = Math.max(0, Math.min(100, feeNum)).toString()

      await db.update(tableUsers).set({ fee: validatedFee }).where(eq(tableUsers.id, userId)).execute()

      return this.getById(userId)
    } catch (error) {
      log.error(`Failed User:updateFee: ${error}`)
      return null
    }
  }

  /**
   * Calculate user-specific fee amount using your specified calculation mechanism
   * Uses your specified approach to avoid overflow/underflow issues
   * @param userId User ID
   * @param tradeAmount Trade amount in the smallest unit
   * @returns Object with amountAfterFee and feeAmount
   */
  static async calculateUserFee(userId: number, tradeAmount: bigint) {
    try {
      const user = await this.getById(userId)

      if (!user || user.fee === "0" || parseFloat(user.fee) === 0) {
        return { amountAfterFee: tradeAmount, feeAmount: BigInt(0) }
      }

      // Use the improved fee calculation to avoid overflow/underflow
      return BlockchainUtils.calculateFeeDeduction(tradeAmount, user.fee)
    } catch (error) {
      log.error(`Failed User:calculateUserFee: ${error}`)
      return { amountAfterFee: tradeAmount, feeAmount: BigInt(0) }
    }
  }

  /**
   * Update user's language preference
   * @param userId User ID
   * @param language Language code (e.g., "en", "es", "fr")
   * @returns Updated user or null if operation failed
   */
  static async updateLanguage(userId: number, language: string) {
    try {
      await db.update(tableUsers).set({ language }).where(eq(tableUsers.id, userId)).execute()

      return this.getById(userId)
    } catch (error) {
      log.error(`Failed User:updateLanguage: ${error}`)
      return null
    }
  }

  /**
   * Get all active users
   * @returns Array of active users
   */
  static async getAllActive() {
    try {
      return await db.select().from(tableUsers).where(eq(tableUsers.isActive, true))
    } catch (error) {
      log.error(`Failed User:getAllActive: ${error}`)
      return []
    }
  }
}
